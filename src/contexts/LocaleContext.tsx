"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import enTranslations from '../locales/en.json';
import arTranslations from '../locales/ar.json';

type Locale = 'en' | 'ar';
type Translations = typeof enTranslations;

interface LocaleContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: (key: string, variables?: Record<string, string>) => string;
  dir: string;
  isClient: boolean;
}

const LocaleContext = createContext<LocaleContextType | undefined>(undefined);

const translations: Record<Locale, Translations> = {
  en: enTranslations,
  ar: arTranslations,
};

const LOCALE_STORAGE_KEY = 'barcode-cafe-locale';

export const LocaleProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Always start with 'en' as the default for server-side rendering
  const [locale, setLocale] = useState<Locale>('en');
  const [dir, setDir] = useState<'ltr' | 'rtl'>('ltr');
  // Track if we're on the client or not for conditional rendering
  const [isClient, setIsClient] = useState(false);

  // Initialize client-side state after mounting
  useEffect(() => {
    setIsClient(true);
    
    // Check localStorage after component mounts (client-side only)
    const savedLocale = localStorage.getItem(LOCALE_STORAGE_KEY);
    if (savedLocale === 'ar') {
      setLocale('ar');
    }
  }, []);

  // Effect to update the direction and HTML attributes when locale changes
  useEffect(() => {
    // Only run this on the client
    if (!isClient) return;

    const direction = locale === 'ar' ? 'rtl' : 'ltr';
    setDir(direction);
    
    // Save to localStorage whenever locale changes
    localStorage.setItem(LOCALE_STORAGE_KEY, locale);
    
    // Set the dir attribute on HTML element
    document.documentElement.dir = direction;
    
    // Set lang attribute for proper font rendering and accessibility
    document.documentElement.lang = locale;
    
    // Add or remove Tailwind RTL classes
    if (locale === 'ar') {
      document.documentElement.classList.add('font-arabic');
    } else {
      document.documentElement.classList.remove('font-arabic');
    }

  }, [locale, isClient]);

  // Function to update the locale
  const changeLocale = (newLocale: Locale) => {
    setLocale(newLocale);
  };

  // Function to get a translation by nested key (e.g. "signin.title")
  const t = (key: string, variables?: Record<string, string>): string => {
    const keys = key.split('.');
    let result: unknown = translations[locale];
    
    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        result = (result as Record<string, unknown>)[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }
    
    if (typeof result === 'string') {
      let translatedText = result;
      
      // Replace variables if provided
      if (variables) {
        Object.entries(variables).forEach(([varName, varValue]) => {
          translatedText = translatedText.replace(`{{${varName}}}`, varValue);
        });
      }
      
      return translatedText;
    }
    
    return key;
  };

  return (
    <LocaleContext.Provider value={{ locale, setLocale: changeLocale, t, dir, isClient }}>
      {children}
    </LocaleContext.Provider>
  );
};

export const useLocale = (): LocaleContextType => {
  const context = useContext(LocaleContext);
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider');
  }
  return context;
}; 